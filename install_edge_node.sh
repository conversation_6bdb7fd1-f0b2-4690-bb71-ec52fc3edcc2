#!/bin/bash

# This script installs prerequisites for KubeEdge EdgeCore on Ubuntu based on the provided Tencent Cloud article.
# It includes installing Docker and configuring KubeEdge EdgeCore.
# Note: This script covers the steps mentioned in the article but might need adjustments for a full KubeEdge deployment.
# A full KubeEdge EdgeCore installation typically involves 'keadm join'.

# --- Configuration ---
UBUNTU_VERSION="bionic" # e.g., bionic for Ubuntu 18.04
KUBEEDGE_VERSION="1.1.0" # e.g., 1.1.0 (version mentioned in article)
CLOUDCORE_IP="<CloudCore-IP>" # Replace with the actual IP address of your cloud node
TOKEN="<Token>" # Replace with the token obtained from 'kubeadm init' on the cloud node

# --- Update package list and install dependencies ---
echo "Updating package list and installing dependencies..."
apt-get update
apt-get install -y apt-transport-https ca-certificates curl gnupg-agent software-properties-common

# --- Install Docker ---
echo "Installing Docker..."
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | apt-key add -
add-apt-repository "deb [arch=amd64] https://download.docker.com/linux/ubuntu ${UBUNTU_VERSION} stable"
apt-get update
# Find the exact Docker CE version
DOCKER_CE_VERSION=$(apt-cache madison 'docker-ce' | grep 19.03.4 | head -1 | awk '{$1=$1};1' | cut -d' ' -f 3)
apt-get install -y --allow-change-held-packages --allow-downgrades docker-ce=${DOCKER_CE_VERSION}
systemctl enable docker
systemctl start docker

# --- Install KubeEdge EdgeCore ---
echo "Installing KubeEdge EdgeCore..."
# The article does not detail manual EdgeCore installation steps.
# The standard way is using keadm join.
# Example using keadm join (requires keadm to be installed separately):
# keadm join --cloudcore-ipport=${CLOUDCORE_IP}:10000 --token=${TOKEN} --kubeedge-version=${KUBEEDGE_VERSION}
# Replace <CloudCore-IP> and <Token> with your actual values.

echo "Edge node setup script finished. Please manually install KubeEdge EdgeCore using keadm join."
echo "Remember to replace <CloudCore-IP> and <Token> with your actual values."