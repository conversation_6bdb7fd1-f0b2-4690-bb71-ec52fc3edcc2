#!/bin/bash

# This script installs prerequisites for KubeEdge CloudCore on Ubuntu based on the provided Tencent Cloud article.
# It includes installing Docker, Kubernetes components, and generating KubeEdge certificates.
# Note: This script covers the steps mentioned in the article but might need adjustments for a full KubeEdge deployment.
# A full KubeEdge CloudCore installation typically involves 'keadm init'.

# --- Configuration ---
UBUNTU_VERSION="bionic" # e.g., bionic for Ubuntu 18.04
KUBE_VERSION="1.16.2" # e.g., 1.16.2
KUBEEDGE_VERSION="1.1.0" # e.g., 1.1.0 (version mentioned in article)
CA_PATH="/etc/kubeedge/ca"
CERT_PATH="/etc/kubeedge/certs"
CA_SUBJECT="/C=CN/ST=Zhejiang/L=Hangzhou/O=KubeEdge/CN=kubeedge.io"
SUBJECT="/C=CN/ST=Zhejiang/L=Hangzhou/O=KubeEdge/CN=kubeedge.io"

# --- Update package list and install dependencies ---
echo "Updating package list and installing dependencies..."
apt-get update
apt-get install -y apt-transport-https ca-certificates curl gnupg-agent software-properties-common

# --- Install Docker ---
echo "Installing Docker..."
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | apt-key add -
add-apt-repository "deb [arch=amd64] https://download.docker.com/linux/ubuntu ${UBUNTU_VERSION} stable"
apt-get update
# Find the exact Docker CE version
DOCKER_CE_VERSION=$(apt-cache madison 'docker-ce' | grep 19.03.4 | head -1 | awk '{$1=$1};1' | cut -d' ' -f 3)
apt-get install -y --allow-change-held-packages --allow-downgrades docker-ce=${DOCKER_CE_VERSION}
systemctl enable docker
systemctl start docker

# --- Install kubeadm, kubelet, kubectl ---
echo "Installing Kubernetes components (kubeadm, kubelet, kubectl)..."
curl https://mirrors.aliyun.com/kubernetes/apt/doc/apt-key.gpg | apt-key add -
cat <<EOF >/etc/apt/sources.list.d/kubernetes.list
deb https://mirrors.aliyun.com/kubernetes/apt/ kubernetes-xenial main
EOF
apt-get update
# Find the exact Kubernetes version
KUBE_EXACT_VERSION=$(apt-cache madison 'kubeadm' | grep ${KUBE_VERSION} | head -1 | awk '{$1=$1};1' | cut -d' ' -f 3)
apt-get install -y --allow-change-held-packages --allow-downgrades kubeadm=${KUBE_EXACT_VERSION} kubelet=${KUBE_EXACT_VERSION} kubectl=${KUBE_EXACT_VERSION}

# --- Initialize Kubernetes Control Plane (Master Node) ---
echo "Initializing Kubernetes control plane..."
# Replace <pod-network-cidr> with your desired Pod network CIDR (e.g., 10.244.0.0/16 for Flannel)
kubeadm init --pod-network-cidr=<pod-network-cidr>

# --- Configure kubectl for the current user ---
echo "Configuring kubectl..."
mkdir -p $HOME/.kube
sudo cp -i /etc/kubernetes/admin.conf $HOME/.kube/config
sudo chown $(id -u):$(id -g) $HOME/.kube/config

# --- Install a Pod network add-on (e.g., Flannel) ---
echo "Installing Pod network add-on (Flannel)..."
# Apply the CNI network plugin YAML. Example for Flannel:
# kubectl apply -f https://raw.githubusercontent.com/coreos/flannel/master/Documentation/kube-flannel.yml
# Choose a CNI plugin and uncomment the appropriate line or add your own.
# kubectl apply -f <your-cni-yaml-file>

# --- Generate KubeEdge Certificates ---
echo "Generating KubeEdge certificates..."
mkdir -p ${CA_PATH}
mkdir -p ${CERT_PATH}

# Certificate Generation Script from the article
cat <<'EOF_CERTGEN' > /etc/kubeedge/certgen.sh
#!/bin/sh

readonly caPath=${CA_PATH:-/etc/kubeedge/ca}
readonly caSubject=${CA_SUBJECT:-/C=CN/ST=Zhejiang/L=Hangzhou/O=KubeEdge/CN=kubeedge.io}
readonly certPath=${CERT_PATH:-/etc/kubeedge/certs}
readonly subject=${SUBJECT:-/C=CN/ST=Zhejiang/L=Hangzhou/O=KubeEdge/CN=kubeedge.io}

genCA() { 
   
    openssl genrsa -des3 -out ${caPath}/rootCA.key -passout pass:kubeedge.io 4096
    openssl req -x509 -new -nodes -key ${caPath}/rootCA.key -sha256 -days 3650 \
    -subj "${caSubject}" -passin pass:kubeedge.io -out ${caPath}/rootCA.crt
}

ensureCA() { 
   
    if [ ! -e ${caPath}/rootCA.key ] || [ ! -e ${caPath}/rootCA.crt ]; then
        genCA
    fi
}

ensureFolder() { 
   
    if [ ! -d ${caPath} ]; then
        mkdir -p ${caPath}
    fi
    if [ ! -d ${certPath} ]; then
        mkdir -p ${certPath}
    fi
}

genCertAndKey() { 
   
    ensureFolder
    ensureCA
    local name=$1
    openssl genrsa -out ${certPath}/${name}.key 2048
    openssl req -new -key ${certPath}/${name}.key -subj "${subject}" -out ${certPath}/${name}.csr
    openssl x509 -req -in ${certPath}/${name}.csr -CA ${caPath}/rootCA.crt -CAkey ${caPath}/rootCA.key \
    -CAcreateserial -passin pass:kubeedge.io -out ${certPath}/${name}.crt -days 365 -sha256
}

buildSecret() { 
   
    local name="edge"
    genCertAndKey ${name} > /dev/null 2>&1
    cat <<EOF_SECRET
apiVersion: v1
kind: Secret
metadata:
  name: cloudcore
  namespace: kubeedge
  labels:
    k8s-app: kubeedge
    kubeedge: cloudcore
stringData:
  rootCA.crt: |
$(pr -T -o 4 ${caPath}/rootCA.crt)
  edge.crt: |
$(pr -T -o 4 ${certPath}/${name}.crt)
  edge.key: |
$(pr -T -o 4 ${certPath}/${name}.key)
EOF_SECRET
}

# Execute the requested function
if [ "$#" -eq 0 ]; then
    echo "Usage: $0 <function> [args]"
    exit 1
fi

case "$1" in
    genCA)
        genCA
        ;;
    ensureCA)
        ensureCA
        ;;
    ensureFolder)
        ensureFolder
        ;;
    genCertAndKey)
        if [ "$#" -ne 2 ]; then
            echo "Usage: $0 genCertAndKey <name>"
            exit 1
        fi
        genCertAndKey $2
        ;;
    buildSecret)
        buildSecret
        ;;
    *)
        echo "Unknown function: $1"
        exit 1
        ;;
esac

EOF_CERTGEN

chmod +x /etc/kubeedge/certgen.sh

# Generate edge certificate and key
bash /etc/kubeedge/certgen.sh genCertAndKey edge

# Create the cloudcore secret in Kubernetes
echo "Creating cloudcore secret in Kubernetes..."
bash /etc/kubeedge/certgen.sh buildSecret | kubectl apply -f -

# --- Install KubeEdge CloudCore ---
echo "Installing KubeEdge CloudCore..."
# The article does not detail manual CloudCore installation steps.
# The standard way is using keadm init.
# Example using keadm init (requires keadm to be installed separately):
# keadm init --advertise-address=<CloudCore-IP> --kubeedge-version=${KUBEEDGE_VERSION}
# Replace <CloudCore-IP> with the actual IP address of your cloud node.

echo "Cloud node setup script finished. Please manually install KubeEdge CloudCore using keadm init or other methods."
echo "Remember to replace <pod-network-cidr> in kubeadm init and <CloudCore-IP> in keadm init with your actual values."