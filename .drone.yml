kind: pipeline
type: docker
name: ec-dashbaord-publish
steps:
- name: build
  image: plugins/docker
  volumes:
  - name: hosts
    path: /etc/hosts
  - name: docker-ca
    path: /etc/docker
  - name: dockersock
    path: /var/run/docker.sock
  settings:
    username: admin
    password:
      from_secret: harbor_password
    repo: edge.imooc.com/imooc_containers/ec-dashboard
    registry: edge.imooc.com
    tags:
    - v2
- name: ssh commands
  image: appleboy/drone-ssh
  settings:
    host: **************
    username: root
    password:
      from_secret: ssh_password
    port: 22
    script:
    - kubectl get deploy -n ec-dashboard
    #更新镜像升级
    - kubectl set image deployment/ec-dashboard ec-dashboard=edge.imooc.com/imooc_containers/ec-dashboard:v2 -n ec-dashboard
volumes:
- name: hosts
  host:
    path: /etc/hosts
- name: docker-ca
  host:
    path: /etc/docker
- name: dockersock
  host:
    path: /var/run/docker.sock