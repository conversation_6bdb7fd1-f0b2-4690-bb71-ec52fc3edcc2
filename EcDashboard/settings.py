"""
Django settings for EcDashboard project.

Generated by 'django-admin startproject' using Django 3.2.12.

For more information on this file, see
https://docs.djangoproject.com/en/3.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.2/ref/settings/
"""

from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-y2+cp1ckjfly+a4!v7ph4i$d(prtxs8!ki58z3d!&#*4eb7h*a'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ["*"]

# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    # 跨域配置
    'corsheaders',
    'apps.base',
    'apps.node',
    'apps.metrics',
    'apps.pod',
    'apps.device',
    'apps.router',
    'apps.ai_model',
    'apps.svc_data',

]
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',),
    'DEFAULT_PERMISSION_CLASSES': (
        'rest_framework.permissions.IsAuthenticated',
    )
}

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.common.CommonMiddleware",
]

ROOT_URLCONF = 'EcDashboard.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'EcDashboard.wsgi.application'

# Database
# https://docs.djangoproject.com/en/3.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ec-dashboard-db',
        'HOST': '*************',
        'USER': 'root',
        'PASSWORD': 'imooc@123',
        'TEST': {
            'NAME': 'ec-dashboard-db-test',
            'CHARSET': 'utf8',
            'COLLATION': 'utf8_general_ci'
        }
    }
}

# Password validation
# https://docs.djangoproject.com/en/3.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]
from datetime import timedelta

SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(days=7),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=1),
    'ROTATE_REFRESH_TOKENS': False,
    'BLACKLIST_AFTER_ROTATION': False,
    'UPDATE_LAST_LOGIN': False,

    'ALGORITHM': 'HS256',
    'SIGNING_KEY': SECRET_KEY,
    'VERIFYING_KEY': None,
    'AUDIENCE': None,
    'ISSUER': None,
    'JWK_URL': None,
    'LEEWAY': 0,

    'AUTH_HEADER_TYPES': ('Bearer',),
    'AUTH_HEADER_NAME': 'HTTP_AUTHORIZATION',
    'USER_ID_FIELD': 'id',
    'USER_ID_CLAIM': 'user_id',
    'USER_AUTHENTICATION_RULE': 'rest_framework_simplejwt.authentication.default_user_authentication_rule',

    'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),
    'TOKEN_TYPE_CLAIM': 'token_type',
    'TOKEN_USER_CLASS': 'rest_framework_simplejwt.models.TokenUser',

    'JTI_CLAIM': 'jti',

    'SLIDING_TOKEN_REFRESH_EXP_CLAIM': 'refresh_exp',
    'SLIDING_TOKEN_LIFETIME': timedelta(days=7),
    'SLIDING_TOKEN_REFRESH_LIFETIME': timedelta(days=1),
}

# Internationalization
# https://docs.djangoproject.com/en/3.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_L10N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.2/howto/static-files/

STATIC_URL = '/static/'

# Default primary key field type
# https://docs.djangoproject.com/en/3.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

LOG_DIR = BASE_DIR / "logs"
LOG_DIR.mkdir(exist_ok=True)
# 日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,  # 是否禁用日志器

    # 日志格式
    'formatters': {
        # 详细格式，名字可随意取
        'verbose': {
            # 格式：	报错等级	       报错时间	   模块	       行号	  信息
            'format': '%(levelname)s %(asctime)s %(module)s %(lineno)d %(message)s',
            'datefmt': '%m-%d %H:%M:%S',
        },
        # 简单格式
        'simple': {
            'format': '%(levelname)s %(module)s %(lineno)d %(message)s'
        },
    },

    # 过滤器
    'filters': {
        'require_debug_true': {
            '()': 'django.utils.log.RequireDebugTrue',  # 过滤掉调试信息
        },
    },

    # 处理错误信息的对象
    'handlers': {
        'console': {  # 处理错误信息的控制台对象，即我们在pycharm中看到的调试信息由此控制
            'level': 'DEBUG',  # DEBUG以上级别报错信息都会出现在控制台
            'filters': ['require_debug_true'],  # 设定过滤器
            'class': 'logging.StreamHandler',  # 日志处理器
            'formatter': 'verbose'  # 日志格式设为详细格式
        },
        'file': {  # 处理错误信息的file对象
            'level': 'INFO',  # 记录INFO级别以上的报错信息
            'class': 'logging.handlers.RotatingFileHandler',  # 日志处理器
            'filename': LOG_DIR / "ec-dashbaord.log",
            # 日志位置,日志文件名,日志保存目录logs必须手动创建，一般创建在项目根目录下
            'maxBytes': 300 * 1024 * 1024,  # 日志文件的最大值,这里我们设置300M
            'backupCount': 10,  # 日志文件的数量,设置最大日志数量为10
            'formatter': 'verbose'  # 日志格式:详细格式，日志格式在上面定义了
        },
    },

    # 日志对象
    'loggers': {
        # 日志器名
        'django': {
            'handlers': ['console', 'file'],  # 处理对象有两个，一个conosle对象，一个file对象，这两个对象在上面的handlers中进行了定义和配置
            'propagate': True,  # 是否让日志信息继续冒泡给其他的日志处理系统，子级向父级传播
        },
    }
}
# 跨域配置
CORS_ALLOW_CREDENTIALS = True
CORS_ORIGIN_ALLOW_ALL = True
CORS_ALLOW_METHODS = [
    "DELETE",
    "GET",
    "OPTIONS",
    "PATCH",
    "POST",
    "PUT",
]
CORS_ALLOW_HEADERS = [
    "accept",
    "accept-encoding",
    "authorization",
    "content-type",
    "dnt",
    "origin",
    "user-agent",
    "x-csrftoken",
    "x-requested-with",
]

from kubernetes import client, config

# Configs can be set in Configuration class directly or using helper utility
# config.load_kube_config()
host = "https://**************:6443"
# 云端rest访问地址
CLOUD_ROUTER_HOST = "http://**************:9443"
with open(BASE_DIR / "k8s_config" / "token", "r")as f:
    token = f.read()
ssl_ca_cert = BASE_DIR / "k8s_config" / "ca.crt"
configuration = client.Configuration()
configuration.host = host
configuration.api_key = {
    'authorization': "Bearer " + token
}
configuration.ssl_ca_cert = ssl_ca_cert
# 开启证书验证
configuration.verify_ssl = True
client.Configuration.set_default(configuration)
APISERVER_CLIENT = client
# 模型的保存路径
MODELS_PATH = BASE_DIR / "models"
