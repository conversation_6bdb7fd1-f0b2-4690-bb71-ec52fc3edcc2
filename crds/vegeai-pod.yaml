apiVersion: v1
kind: Pod
metadata:
  labels:
    kubeedge-app: vegetables-analyzer
  name: vegetables-analyzer
  namespace: default
spec:
  #指定边缘节点
  nodeName: imooc-edge02
  hostNetwork: true
  restartPolicy: Always
  #镜像拉取策略
  imagePullSecrets:
  - name: docker-secret
  containers:
  - name: vegetables-analyzer
    image: edge.imooc.com/imooc_containers/vegetables_analyzer:v8
    imagePullPolicy: IfNotPresent
    env:
      #绑定设备名称
    - name: DEVICE_NAME
      value: vege-device
      #数据上报Topic
    - name: TOPIC_UPLOAD
      value: default/upload
    #挂载本地python依赖包
    volumeMounts:
    - mountPath: /usr/local/lib/python3.7/site-packages
      name: site-pkg
  volumes:
  - name: site-pkg
    hostPath:
      path: /root/volume_sitepkg/site-packages
