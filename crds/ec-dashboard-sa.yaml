apiVersion: v1
kind: Namespace
metadata:
  name: ec-dashboard

---

apiVersion: v1
kind: ServiceAccount
metadata:
  name: ec-dashboard-admin
  namespace: ec-dashboard

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: ec-dashboard-admin-crb
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cluster-admin
subjects:
- kind: ServiceAccount
  name: ec-dashboard-admin
  namespace: ec-dashboard